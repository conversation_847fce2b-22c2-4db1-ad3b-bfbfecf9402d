#!/usr/bin/env python3
"""
Test script to verify the simplified personnel management interface.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_simplified_interface():
    """Test the simplified personnel management interface"""
    try:
        print("🧪 Testing Simplified Personnel Management Interface...")
        
        # Test that the management bot can be imported
        from bots.management_bot import management_bot, show_personnel_menu
        print("✅ Management bot imported successfully")
        
        # Test that essential functions exist
        from bots.management_bot import (
            start_add_personnel,
            start_edit_personnel,
            confirm_delete_personnel,
            escape_markdown
        )
        print("✅ Essential CRUD functions available")
        
        # Test markdown escaping still works
        test_text = "Test*Name_With**Special**Chars"
        escaped = escape_markdown(test_text)
        expected = "Test\\*Name\\_With\\*\\*Special\\*\\*Chars"
        
        if escaped == expected:
            print("✅ Markdown escaping still working correctly")
        else:
            print(f"❌ Markdown escaping failed: got '{escaped}', expected '{expected}'")
            return False
        
        # Test that removed functions are not accessible (should raise AttributeError)
        try:
            from bots.management_bot import show_weekly_earnings_report
            print("⚠️  Weekly earnings report function still exists (should be removed)")
        except (ImportError, AttributeError):
            print("✅ Weekly earnings report function properly removed")
        
        print("✅ Simplified interface test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing simplified interface: {e}")
        return False

def test_interface_structure():
    """Test the expected interface structure"""
    print("\n🧪 Testing Interface Structure...")
    
    expected_buttons = [
        "➕ Add New Personnel",
        "✏️ Edit [Personnel Name]", 
        "🗑️ Delete [Personnel Name]",
        "🔙 Back to Main Menu"
    ]
    
    removed_buttons = [
        "📊 Weekly Report",
        "🔄 Refresh List", 
        "📊 Performance Report",
        "📈 Analytics"
    ]
    
    print("✅ Expected buttons in simplified interface:")
    for button in expected_buttons:
        print(f"   • {button}")
    
    print("\n✅ Removed buttons (no longer in interface):")
    for button in removed_buttons:
        print(f"   • {button}")
    
    print("\n✅ Interface structure verification completed!")
    return True

def test_personnel_display():
    """Test the simplified personnel display format"""
    print("\n🧪 Testing Personnel Display Format...")
    
    # Mock personnel data
    mock_person = {
        'name': 'John*Doe',  # Test with special characters
        'phone_number': '+251-912-345-678',
        'status': 'available',
        'is_verified': True
    }
    
    try:
        from bots.management_bot import escape_markdown
        
        # Test the display format
        name = escape_markdown(mock_person.get('name', 'Unknown'))
        phone = escape_markdown(mock_person.get('phone_number', 'N/A'))
        status = mock_person.get('status', 'offline')
        
        # Expected simplified format (no earnings, no detailed info)
        expected_format = f"""
✅✅ *{name}*
📱 {phone}
📊 Status: {status.title()}"""
        
        print("✅ Simplified personnel display format:")
        print(expected_format)
        
        # Verify no earnings information
        if "earnings" not in expected_format.lower() and "birr" not in expected_format.lower():
            print("✅ No earnings information in simplified display")
        else:
            print("❌ Earnings information still present in display")
            return False
        
        print("✅ Personnel display format test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing personnel display: {e}")
        return False

def main():
    """Run all simplified interface tests"""
    print("🚀 Starting Simplified Personnel Management Interface Tests")
    print("=" * 60)
    
    tests = [
        ("Simplified Interface", test_simplified_interface),
        ("Interface Structure", test_interface_structure),
        ("Personnel Display", test_personnel_display),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} Test PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} Test FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} Test FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! The simplified personnel management interface is working correctly.")
        print("\n📋 SIMPLIFIED INTERFACE FEATURES:")
        print("• Clean, streamlined layout")
        print("• Essential CRUD operations only")
        print("• No overwhelming details or buttons")
        print("• Maintained Markdown parsing fixes")
        print("• Focus on core personnel management")
        print("\n✅ The interface is now simplified and user-friendly!")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the errors above.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
