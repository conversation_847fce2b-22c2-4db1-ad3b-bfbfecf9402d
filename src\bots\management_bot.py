"""
Management Bot for Wiz Aroma Delivery System
Comprehensive management interface for delivery personnel, analytics, and system oversight.
Replaces the previous notification bot with full management capabilities.
Access restricted to authorized management Telegram IDs.
"""

import telebot
from telebot import types
import datetime
from typing import Dict, List, Any, Optional
import logging
import sys
import os
import time
from collections import defaultdict, deque
import json

from src.config import logger

from src.firebase_db import get_data, set_data, delete_data
from src.data_models import (
    delivery_personnel,
    delivery_personnel_assignments,
    delivery_personnel_availability,
    delivery_personnel_capacity,
    delivery_personnel_zones,
    delivery_personnel_performance,
    DeliveryPersonnel
)
from src.utils.delivery_personnel_utils import (
    create_delivery_personnel,
    verify_delivery_personnel,
    get_delivery_personnel_by_telegram_id,
    get_all_delivery_personnel,
    remove_delivery_personnel
)
from src.utils.earnings_utils import (
    get_personnel_earnings_summary,
    get_all_personnel_earnings,
    update_personnel_earnings,
    get_weekly_earnings_report
)
from src.utils.helpers import is_admin

# Management Bot Configuration
MANAGEMENT_BOT_AUTHORIZED_IDS = [7729984017]  # Authorized management user IDs

# Get management bot instance from bot_instance.py to avoid circular imports
def get_management_bot():
    """Get the management bot instance"""
    from src.bot_instance import management_bot
    return management_bot

management_bot = get_management_bot()

# Print bot info for verification
try:
    bot_info = management_bot.get_me()
    print(f"[BOT INFO] Username: @{bot_info.username}, ID: {bot_info.id}")
except Exception as e:
    print(f"[BOT INFO] Could not fetch bot info: {e}")

# Export the management bot instance for use in main.py
__all__ = ['management_bot', 'register_management_bot_handlers']

# Simple in-memory rate limiter
RATE_LIMIT = 10  # actions
RATE_PERIOD = 60  # seconds
user_action_times = defaultdict(lambda: deque(maxlen=RATE_LIMIT))

def is_rate_limited(user_id):
    now = time.time()
    dq = user_action_times[user_id]
    dq.append(now)
    if len(dq) == RATE_LIMIT and now - dq[0] < RATE_PERIOD:
        return True
    return False

def is_authorized_user(user_id: int) -> bool:
    """Check if user is authorized to access management functions"""
    return user_id in MANAGEMENT_BOT_AUTHORIZED_IDS

def create_main_menu_keyboard():
    """Create the main management menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    
    # Delivery Personnel Management
    keyboard.add(
        types.InlineKeyboardButton("👥 Personnel Management", callback_data="mgmt_personnel"),
        types.InlineKeyboardButton("📊 Analytics Dashboard", callback_data="mgmt_analytics")
    )
    
    # System Management
    keyboard.add(
        types.InlineKeyboardButton("📈 Reports", callback_data="mgmt_reports"),
        types.InlineKeyboardButton("💰 Earnings", callback_data="mgmt_earnings")
    )
    
    # Utilities
    keyboard.add(
        types.InlineKeyboardButton("🔄 Refresh Data", callback_data="mgmt_refresh"),
        types.InlineKeyboardButton("ℹ️ System Info", callback_data="mgmt_info")
    )
    return keyboard

def create_personnel_menu_keyboard():
    """Create personnel management menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    
    keyboard.add(
        types.InlineKeyboardButton("➕ Add Personnel", callback_data="pers_add"),
        types.InlineKeyboardButton("➖ Remove Personnel", callback_data="pers_remove")
    )
    keyboard.add(
        types.InlineKeyboardButton("📋 List All", callback_data="pers_list"),
        types.InlineKeyboardButton("🔍 Search", callback_data="pers_search")
    )
    keyboard.add(
        types.InlineKeyboardButton("📊 Performance", callback_data="pers_performance"),
        types.InlineKeyboardButton("🔧 Manage Status", callback_data="pers_status")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
    )
    
    return keyboard

def create_analytics_menu_keyboard():
    """Create analytics dashboard menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    
    keyboard.add(
        types.InlineKeyboardButton("📅 Daily Summary", callback_data="analytics_daily"),
        types.InlineKeyboardButton("📊 Weekly Summary", callback_data="analytics_weekly")
    )
    keyboard.add(
        types.InlineKeyboardButton("📈 Monthly Summary", callback_data="analytics_monthly"),
        types.InlineKeyboardButton("🔢 Transaction Counts", callback_data="analytics_transactions")
    )
    keyboard.add(
        types.InlineKeyboardButton("🚚 Delivery Stats", callback_data="analytics_delivery"),
        types.InlineKeyboardButton("💹 Trend Analysis", callback_data="analytics_trends")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
    )
    
    return keyboard

def log_admin_action(action, details, user_id):
    # Use integer milliseconds timestamp for Firestore-safe document ID
    ts = int(time.time() * 1000)
    log_entry = {
        'timestamp': datetime.datetime.utcnow().isoformat(),
        'user_id': user_id,
        'action': action,
        'details': details
    }
    set_data(f"admin_audit_logs/{ts}_{user_id}", log_entry)

ADMIN_TELEGRAM_ID = 7729984017

def notify_admin_error(error_text):
    try:
        management_bot.send_message(ADMIN_TELEGRAM_ID, f"❗️[ERROR] {error_text}")
    except Exception as e:
        logger.error(f"Failed to notify admin: {e}")

def handle_start(message):
    """Handle start command and show main menu"""
    user_id = message.from_user.id
    
    if not is_admin(user_id):
        management_bot.reply_to(
            message,
            "❌ **Access Denied**\n\n"
            "You are not authorized to access the Management Bot.\n"
            "Contact system administrator for access.",
            parse_mode='Markdown'
        )
        return
    
    if is_rate_limited(user_id):
        management_bot.reply_to(message, "⏳ Too many actions. Please wait a minute and try again.")
        return
    
    try:
        log_admin_action('start_command', {}, user_id)
        
        welcome_text = f"""
🏢 **Wiz Aroma Management Bot**

Welcome to the comprehensive management interface!

**Available Functions:**
👥 **Personnel Management** - Add/remove delivery personnel
📊 **Analytics Dashboard** - View system performance metrics
📈 **Reports** - Generate detailed reports
💰 **Earnings** - Track delivery personnel earnings
🔄 **Data Management** - Refresh and maintain system data

Select an option below to get started:
    """
        
        management_bot.send_message(
            message.chat.id,
            welcome_text,
            reply_markup=create_main_menu_keyboard(),
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Exception in handle_start: {e}")
        notify_admin_error(f"Exception in handle_start: {e}")
        management_bot.reply_to(message, "❗️ An error occurred. The admin has been notified.")

def handle_callback_query(call):
    """Handle all callback queries"""
    user_id = call.from_user.id
    
    if not is_admin(user_id):
        management_bot.answer_callback_query(
            call.id,
            "❌ You are not authorized to use this bot."
        )
        return
    
    if is_rate_limited(user_id):
        management_bot.answer_callback_query(call.id, "⏳ Too many actions. Please wait a minute and try again.")
        return
    
    try:
        log_admin_action('callback', {'data': call.data}, user_id)
        
        # Show processing message for long actions
        if call.data.startswith("mgmt_analytics"):
            management_bot.answer_callback_query(call.id, "⏳ Processing analytics...", show_alert=False)
        
        # Main menu navigation
        if call.data == "mgmt_main":
            show_main_menu(call)
        elif call.data == "mgmt_personnel":
            show_personnel_menu(call)
        elif call.data == "mgmt_analytics":
            show_analytics_menu(call)
        elif call.data == "mgmt_reports":
            show_reports_menu(call)
        elif call.data == "mgmt_earnings":
            show_earnings_menu(call)
        elif call.data == "mgmt_refresh":
            refresh_system_data(call)
        elif call.data == "mgmt_info":
            show_system_info(call)
        
        # Personnel management
        elif call.data.startswith("pers_"):
            handle_personnel_action(call)

        # Individual personnel management
        elif call.data.startswith("pers_manage_"):
            show_individual_personnel_menu(call)
        elif call.data.startswith("pers_view_"):
            show_personnel_details(call)
        elif call.data.startswith("pers_edit_"):
            start_edit_personnel(call)
        elif call.data.startswith("pers_delete_"):
            confirm_delete_personnel(call)
        elif call.data.startswith("confirm_delete_"):
            execute_delete_personnel(call)
        elif call.data.startswith("pers_update_"):
            handle_personnel_update(call)

        # Edit specific fields
        elif call.data.startswith("edit_name_"):
            start_edit_name(call)
        elif call.data.startswith("edit_phone_"):
            start_edit_phone(call)
        elif call.data.startswith("edit_telegram_"):
            start_edit_telegram_id(call)
        elif call.data.startswith("save_edit_"):
            save_personnel_edit(call)

        # Analytics
        elif call.data.startswith("analytics_"):
            handle_analytics_action(call)
        
        # Reports
        elif call.data.startswith("reports_"):
            handle_reports_action(call)
        
        # Earnings
        elif call.data.startswith("earnings_"):
            handle_earnings_action(call)

        # Personnel removal confirmation
        elif call.data.startswith("remove_personnel_"):
            handle_personnel_removal(call)
        elif call.data.startswith("confirm_remove_"):
            confirm_personnel_removal(call)

        else:
            management_bot.answer_callback_query(
                call.id,
                "❓ Unknown action. Please try again.",
                show_alert=True
            )
    
    except Exception as e:
        logger.error(f"Exception in handle_callback_query: {e}")
        notify_admin_error(f"Exception in handle_callback_query: {e}")
        management_bot.answer_callback_query(
            call.id,
            "❗️ An error occurred. The admin has been notified.",
            show_alert=True
        )

def show_main_menu(call):
    """Show the main management menu"""
    # Answer the callback query first
    management_bot.answer_callback_query(call.id, "🏢 Loading main menu...")

    welcome_text = """
🏢 **Wiz Aroma Management Bot**

**Management Dashboard**
Select a function to manage your delivery system:
    """

    management_bot.edit_message_text(
        welcome_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=create_main_menu_keyboard(),
        parse_mode='Markdown'
    )

def show_personnel_menu(call):
    """Show comprehensive personnel management with individual personnel actions"""
    # Answer the callback query first
    management_bot.answer_callback_query(call.id, "👥 Loading personnel management...")

    # Get current personnel data from Firebase
    personnel_data = get_data("delivery_personnel") or {}
    availability_data = get_data("delivery_personnel_availability") or {}
    capacity_data = get_data("delivery_personnel_capacity") or {}

    # Get earnings data for all personnel
    all_earnings = get_all_personnel_earnings()

    if not personnel_data:
        text = """
👥 **Personnel Management**

❌ **No Personnel Found**

There are no delivery personnel registered in the system.

**Available Actions:**
• Add new personnel
• Import personnel data
• Check system configuration
        """
        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("➕ Add New Personnel", callback_data="pers_add")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
        )
    else:
        # Calculate status summary
        status_counts = {"available": 0, "busy": 0, "offline": 0, "inactive": 0}
        verified_count = 0
        total_daily_earnings = 0.0
        total_weekly_earnings = 0.0

        for person in personnel_data.values():
            status = person.get('status', 'offline')
            status_counts[status] = status_counts.get(status, 0) + 1
            if person.get('is_verified', False):
                verified_count += 1

        # Calculate total earnings
        for earnings in all_earnings.values():
            total_daily_earnings += earnings.get('daily_earnings', 0.0)
            total_weekly_earnings += earnings.get('weekly_earnings', 0.0)

        text = f"""
👥 **Personnel Management**

**System Overview:**
• Total Personnel: {len(personnel_data)}
• Verified: {verified_count}
• Available: {status_counts.get('available', 0)}
• Busy: {status_counts.get('busy', 0)}
• Offline: {status_counts.get('offline', 0)}

**Earnings Summary:**
• Today's Total: {total_daily_earnings:.2f} birr
• This Week's Total: {total_weekly_earnings:.2f} birr

**Personnel Details:**
        """

        # Create keyboard with individual personnel management
        keyboard = types.InlineKeyboardMarkup(row_width=1)

        # Add "Add New Personnel" button at top
        keyboard.add(
            types.InlineKeyboardButton("➕ Add New Personnel", callback_data="pers_add")
        )

        # Add individual personnel with comprehensive information
        for personnel_id, person in personnel_data.items():
            name = person.get('name', 'Unknown')
            phone = person.get('phone_number', 'N/A')
            status = person.get('status', 'offline')
            current_orders = capacity_data.get(personnel_id, 0)
            is_verified = person.get('is_verified', False)

            # Get earnings for this personnel
            earnings = all_earnings.get(personnel_id, {})
            daily_earnings = earnings.get('daily_earnings', 0.0)
            weekly_earnings = earnings.get('weekly_earnings', 0.0)

            # Status emoji
            status_emoji = {
                'available': '✅',
                'busy': '🔄',
                'offline': '⭕',
                'inactive': '❌'
            }.get(status, '❓')

            # Verification emoji
            verified_emoji = '✅' if is_verified else '⚠️'

            # Personnel comprehensive summary
            text += f"""
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{status_emoji}{verified_emoji} **{name}**
📱 Phone: {phone}
📦 Active Orders: {current_orders}/5
💰 Today: {daily_earnings:.2f} birr
📊 This Week: {weekly_earnings:.2f} birr
🆔 ID: {personnel_id}
            """

            # Add personnel management button
            keyboard.add(
                types.InlineKeyboardButton(
                    f"{status_emoji} {name} - Manage",
                    callback_data=f"pers_manage_{personnel_id}"
                )
            )

        # Add management action buttons
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh List", callback_data="mgmt_personnel"),
            types.InlineKeyboardButton("📊 Weekly Report", callback_data="pers_weekly_report")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
        )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_analytics_menu(call):
    """Show analytics dashboard menu"""
    # Answer the callback query first
    management_bot.answer_callback_query(call.id, "📊 Loading analytics dashboard...")

    text = """
📊 **Analytics Dashboard**

**Available Analytics:**
• Daily/Weekly/Monthly summaries
• Transaction counts and trends
• Delivery performance metrics
• System usage statistics

Select an analytics option:
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=create_analytics_menu_keyboard(),
        parse_mode='Markdown'
    )

# Placeholder functions for future implementation
def show_reports_menu(call):
    """Show reports menu - to be implemented"""
    management_bot.answer_callback_query(call.id, "📈 Reports feature coming soon!")

def show_earnings_menu(call):
    """Show earnings menu - to be implemented"""
    management_bot.answer_callback_query(call.id, "💰 Earnings feature coming soon!")

def refresh_system_data(call):
    """Refresh system data from Firebase"""
    management_bot.answer_callback_query(call.id, "🔄 Refreshing data...")
    # Implementation will be added in next phase

def show_system_info(call):
    """Show system information"""
    management_bot.answer_callback_query(call.id, "ℹ️ System info feature coming soon!")

def handle_personnel_action(call):
    """Handle personnel management actions"""
    action = call.data.replace("pers_", "")

    if action == "add":
        start_add_personnel(call)
    elif action == "remove":
        start_remove_personnel(call)
    elif action == "list":
        show_personnel_list(call)
    elif action == "search":
        start_personnel_search(call)
    elif action == "performance":
        show_personnel_performance(call)
    elif action == "status":
        show_personnel_status_management(call)
    else:
        management_bot.answer_callback_query(call.id, "❓ Unknown personnel action")

def show_individual_personnel_menu(call):
    """Show management menu for individual personnel"""
    personnel_id = call.data.replace("pers_manage_", "")
    management_bot.answer_callback_query(call.id, "👤 Loading personnel options...")

    # Get personnel data
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    name = person.get('name', 'Unknown')
    status = person.get('status', 'offline')
    is_verified = person.get('is_verified', False)

    # Status and verification emojis
    status_emoji = {
        'available': '✅',
        'busy': '🔄',
        'offline': '⭕',
        'inactive': '❌'
    }.get(status, '❓')

    verified_emoji = '✅' if is_verified else '⚠️'

    text = f"""
👤 **Personnel Management**

**{status_emoji}{verified_emoji} {name}**
ID: `{personnel_id}`

**Quick Actions:**
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("👁️ View Details", callback_data=f"pers_view_{personnel_id}"),
        types.InlineKeyboardButton("✏️ Edit Info", callback_data=f"pers_edit_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔧 Change Status", callback_data=f"pers_status_{personnel_id}"),
        types.InlineKeyboardButton("📊 Performance", callback_data=f"pers_perf_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🗑️ Delete Personnel", callback_data=f"pers_delete_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Personnel List", callback_data="mgmt_personnel")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_personnel_details(call):
    """Show detailed view of personnel information"""
    personnel_id = call.data.replace("pers_view_", "")
    management_bot.answer_callback_query(call.id, "👁️ Loading personnel details...")

    # Get all personnel-related data
    personnel_data = get_data("delivery_personnel") or {}
    availability_data = get_data("delivery_personnel_availability") or {}
    capacity_data = get_data("delivery_personnel_capacity") or {}
    zones_data = get_data("delivery_personnel_zones") or {}
    performance_data = get_data("delivery_personnel_performance") or {}

    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    # Extract personnel information
    name = person.get('name', 'Unknown')
    phone = person.get('phone_number', 'N/A')
    telegram_id = person.get('telegram_id', 'N/A')
    email = person.get('email', 'N/A')
    status = person.get('status', 'offline')
    is_verified = person.get('is_verified', False)
    vehicle_type = person.get('vehicle_type', 'N/A')
    max_capacity = person.get('max_capacity', 5)
    current_capacity = capacity_data.get(personnel_id, 0)
    service_areas = person.get('service_areas', [])
    created_at = person.get('created_at', 'N/A')
    last_active = person.get('last_active', 'N/A')

    # Performance metrics
    perf = performance_data.get(personnel_id, {})
    total_deliveries = perf.get('total_deliveries', person.get('total_deliveries', 0))
    successful_deliveries = perf.get('successful_deliveries', person.get('successful_deliveries', 0))
    rating = perf.get('average_rating', person.get('rating', 5.0))

    # Calculate success rate
    success_rate = (successful_deliveries / total_deliveries * 100) if total_deliveries > 0 else 0

    # Status emojis
    status_emoji = {
        'available': '✅',
        'busy': '🔄',
        'offline': '⭕',
        'inactive': '❌'
    }.get(status, '❓')

    verified_emoji = '✅' if is_verified else '⚠️'

    text = f"""
👁️ **Personnel Details**

**{status_emoji}{verified_emoji} {name}**

**📋 Basic Information:**
• **ID:** `{personnel_id}`
• **Phone:** {phone}
• **Telegram ID:** {telegram_id}
• **Email:** {email}
• **Vehicle:** {vehicle_type.title()}
• **Status:** {status.title()}
• **Verified:** {'Yes' if is_verified else 'No'}

**📊 Capacity & Performance:**
• **Current Orders:** {current_capacity}/{max_capacity}
• **Total Deliveries:** {total_deliveries}
• **Successful:** {successful_deliveries}
• **Success Rate:** {success_rate:.1f}%
• **Rating:** {rating:.1f}/5.0

**🗺️ Service Areas:**
{', '.join(service_areas) if service_areas else 'None assigned'}

**📅 Timeline:**
• **Created:** {created_at}
• **Last Active:** {last_active}
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("✏️ Edit", callback_data=f"pers_edit_{personnel_id}"),
        types.InlineKeyboardButton("🔧 Status", callback_data=f"pers_status_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("📊 Performance", callback_data=f"pers_perf_{personnel_id}"),
        types.InlineKeyboardButton("🗑️ Delete", callback_data=f"pers_delete_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Management", callback_data=f"pers_manage_{personnel_id}")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def handle_analytics_action(call):
    """Handle analytics actions"""
    action = call.data.replace("analytics_", "")

    if action == "daily":
        show_daily_analytics(call)
    elif action == "weekly":
        show_weekly_analytics(call)
    elif action == "monthly":
        show_monthly_analytics(call)
    elif action == "transactions":
        show_transaction_analytics(call)
    elif action == "delivery":
        show_delivery_analytics(call)
    elif action == "trends":
        show_trend_analytics(call)
    else:
        management_bot.answer_callback_query(call.id, "❓ Unknown analytics action")

def start_edit_personnel(call):
    """Start editing personnel information"""
    personnel_id = call.data.replace("pers_edit_", "")
    management_bot.answer_callback_query(call.id, "✏️ Loading edit options...")

    # Get personnel data
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    name = person.get('name', 'Unknown')

    # Get earnings data
    earnings = get_personnel_earnings_summary(personnel_id)

    text = f"""
✏️ **Edit Personnel: {name}**

**Current Information:**
• **Name:** {person.get('name', 'N/A')}
• **Phone:** {person.get('phone_number', 'N/A')}
• **Telegram ID:** {person.get('telegram_id', 'N/A')}
• **Daily Earnings:** {earnings.get('daily_earnings', 0.0):.2f} birr
• **Weekly Earnings:** {earnings.get('weekly_earnings', 0.0):.2f} birr

**⚠️ Note:** Editing preserves all earnings data and performance metrics.

**Select what you want to edit:**
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("📝 Name", callback_data=f"edit_name_{personnel_id}"),
        types.InlineKeyboardButton("📞 Phone", callback_data=f"edit_phone_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("📱 Telegram ID", callback_data=f"edit_telegram_{personnel_id}")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Management", callback_data="mgmt_personnel")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def confirm_delete_personnel(call):
    """Show confirmation dialog for personnel deletion"""
    personnel_id = call.data.replace("pers_delete_", "")
    management_bot.answer_callback_query(call.id, "🗑️ Preparing deletion confirmation...")

    # Get personnel data
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    name = person.get('name', 'Unknown')
    phone = person.get('phone_number', 'N/A')
    status = person.get('status', 'offline')
    total_deliveries = person.get('total_deliveries', 0)

    # Get earnings data
    earnings = get_personnel_earnings_summary(personnel_id)

    text = f"""
⚠️ **Confirm Personnel Deletion**

**Personnel to Delete:**
• **Firestore ID:** `{personnel_id}`
• **Name:** {name}
• **Phone:** {phone}
• **Status:** {status.title()}
• **Total Deliveries:** {total_deliveries}
• **Daily Earnings:** {earnings.get('daily_earnings', 0.0):.2f} birr
• **Weekly Earnings:** {earnings.get('weekly_earnings', 0.0):.2f} birr
• **Lifetime Earnings:** {earnings.get('total_lifetime_earnings', 0.0):.2f} birr

**⚠️ WARNING:**
This action will permanently remove this personnel from the system.
**ALL DATA WILL BE DELETED:**
• Personnel record
• Earnings history (daily, weekly, lifetime)
• Performance metrics
• Availability status
• Zone assignments
• Capacity information

**This action cannot be undone!**

Are you sure you want to proceed?
    """

    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("✅ Yes, Delete", callback_data=f"confirm_delete_{personnel_id}"),
        types.InlineKeyboardButton("❌ Cancel", callback_data=f"pers_view_{personnel_id}")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def handle_reports_action(call):
    """Handle reports actions - to be implemented"""
    management_bot.answer_callback_query(call.id, "📈 Reports feature coming soon!")

def handle_earnings_action(call):
    """Handle earnings actions - to be implemented"""
    management_bot.answer_callback_query(call.id, "💰 Earnings feature coming soon!")

def execute_delete_personnel(call):
    """Execute personnel deletion after confirmation"""
    personnel_id = call.data.replace("confirm_delete_", "")
    management_bot.answer_callback_query(call.id, "🗑️ Deleting personnel...")

    try:
        # Get personnel data before deletion for logging
        personnel_data = get_data("delivery_personnel") or {}
        person = personnel_data.get(personnel_id)

        if not person:
            management_bot.answer_callback_query(
                call.id,
                "❌ Personnel not found!",
                show_alert=True
            )
            return

        name = person.get('name', 'Unknown')

        # Delete from all Firebase collections
        from src.firebase_db import delete_data

        success = True
        error_messages = []

        # Delete from main personnel collection
        if not delete_data(f"delivery_personnel/{personnel_id}"):
            success = False
            error_messages.append("delivery_personnel")

        # Delete from availability
        if not delete_data(f"delivery_personnel_availability/{personnel_id}"):
            error_messages.append("availability")

        # Delete from capacity
        if not delete_data(f"delivery_personnel_capacity/{personnel_id}"):
            error_messages.append("capacity")

        # Delete from zones
        if not delete_data(f"delivery_personnel_zones/{personnel_id}"):
            error_messages.append("zones")

        # Delete from performance
        if not delete_data(f"delivery_personnel_performance/{personnel_id}"):
            error_messages.append("performance")

        # Delete from earnings
        if not delete_data(f"delivery_personnel_earnings/{personnel_id}"):
            error_messages.append("earnings")

        # Also remove from the main collections using alternative method
        try:
            # Get and update main collections
            personnel_data = get_data("delivery_personnel") or {}
            availability_data = get_data("delivery_personnel_availability") or {}
            capacity_data = get_data("delivery_personnel_capacity") or {}
            zones_data = get_data("delivery_personnel_zones") or {}
            performance_data = get_data("delivery_personnel_performance") or {}
            earnings_data = get_data("delivery_personnel_earnings") or {}

            # Remove from all collections
            personnel_data.pop(personnel_id, None)
            availability_data.pop(personnel_id, None)
            capacity_data.pop(personnel_id, None)
            zones_data.pop(personnel_id, None)
            performance_data.pop(personnel_id, None)
            earnings_data.pop(personnel_id, None)

            # Save updated collections
            set_data("delivery_personnel", personnel_data)
            set_data("delivery_personnel_availability", availability_data)
            set_data("delivery_personnel_capacity", capacity_data)
            set_data("delivery_personnel_zones", zones_data)
            set_data("delivery_personnel_performance", performance_data)
            set_data("delivery_personnel_earnings", earnings_data)

        except Exception as e:
            logger.error(f"Error during collection cleanup: {e}")
            error_messages.append("collection_cleanup")

        if success and not error_messages:
            text = f"""
✅ **Personnel Deleted Successfully**

**{name}** (ID: `{personnel_id}`) has been permanently removed from the system.

**Deleted Data:**
• Personnel record
• Earnings history (daily, weekly, lifetime)
• Performance metrics
• Availability status
• Capacity information
• Zone assignments

The personnel can no longer access the delivery system and all their data has been permanently removed from Firebase.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel List", callback_data="mgmt_personnel")
            )

        else:
            text = f"""
⚠️ **Partial Deletion Warning**

**{name}** was deleted from the main personnel database, but some related data may still exist:

**Failed to delete from:** {', '.join(error_messages)}

The personnel cannot access the system, but manual cleanup may be required.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel List", callback_data="mgmt_personnel")
            )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        management_bot.edit_message_text(
            f"❌ **Error Deleting Personnel**\n\n"
            f"An error occurred while deleting the personnel:\n{str(e)}\n\n"
            f"Please try again or contact system administrator.",
            call.message.chat.id,
            call.message.message_id,
            reply_markup=types.InlineKeyboardMarkup().add(
                types.InlineKeyboardButton("🔙 Back to Personnel List", callback_data="mgmt_personnel")
            ),
            parse_mode='Markdown'
        )

# Personnel Management Functions
def start_add_personnel(call):
    """Start the process of adding new delivery personnel"""
    management_bot.answer_callback_query(call.id, "➕ Starting personnel addition...")

    text = """
➕ **Add New Delivery Personnel**

Please provide the following information in this format:

```
Name: [Full Name]
Phone: [Phone Number with country code]
Telegram ID: [Telegram User ID]
```

**Example:**
```
Name: John Doe
Phone: +251912345678
Telegram ID: 123456789
```

**Note:**
• Firestore will automatically assign a unique document ID
• Personnel will be initialized with default settings
• Earnings tracking will be automatically set up
• Service areas can be configured later

Reply to this message with the personnel information.
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Set up message handler for personnel data
    management_bot.register_next_step_handler(call.message, process_add_personnel)

def process_add_personnel(message):
    """Process the personnel addition request"""
    try:
        # Parse the personnel data
        lines = message.text.strip().split('\n')
        personnel_data = {}

        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip()
                personnel_data[key] = value

        # Validate required fields
        required_fields = ['name', 'phone', 'telegram id']
        missing_fields = [field for field in required_fields if field not in personnel_data]

        if missing_fields:
            management_bot.reply_to(
                message,
                f"❌ **Missing Information**\n\n"
                f"Please provide: {', '.join(missing_fields)}\n\n"
                f"Use the format shown in the previous message.",
                parse_mode='Markdown'
            )
            return

        # Validate Telegram ID format
        telegram_id = personnel_data['telegram id']
        if not telegram_id.isdigit():
            management_bot.reply_to(
                message,
                f"❌ **Invalid Telegram ID**\n\n"
                f"Telegram ID must be numeric. You provided: {telegram_id}\n\n"
                f"Please provide a valid Telegram ID.",
                parse_mode='Markdown'
            )
            return

        # Check if Telegram ID already exists
        existing_personnel = get_data("delivery_personnel") or {}
        for pid, pdata in existing_personnel.items():
            if pdata.get('telegram_id') == telegram_id:
                management_bot.reply_to(
                    message,
                    f"❌ **Telegram ID Already Exists**\n\n"
                    f"A personnel with Telegram ID {telegram_id} already exists.\n\n"
                    f"Personnel: {pdata.get('name', 'Unknown')} (ID: {pid})",
                    parse_mode='Markdown'
                )
                return

        # Validate phone number format (basic validation)
        phone = personnel_data['phone']
        if not phone.startswith('+') and not phone.startswith('0'):
            management_bot.reply_to(
                message,
                f"❌ **Invalid Phone Number**\n\n"
                f"Phone number should start with '+' (international) or '0' (local).\n\n"
                f"You provided: {phone}",
                parse_mode='Markdown'
            )
            return

        # Create delivery personnel using Firestore auto-ID
        import uuid
        from src.data_models import DeliveryPersonnel, DeliveryPersonnelEarnings

        # Generate unique personnel ID using Firestore-style auto-ID
        personnel_id = f"dp_{uuid.uuid4().hex[:8]}"

        # Create personnel object
        personnel = DeliveryPersonnel(personnel_id)
        personnel.name = personnel_data['name']
        personnel.phone_number = phone
        personnel.telegram_id = telegram_id
        personnel.service_areas = ['1']  # Default to area 1, can be changed later
        personnel.vehicle_type = 'motorcycle'  # Default vehicle type
        personnel.max_capacity = 5
        personnel.status = 'offline'
        personnel.is_verified = False

        # Save to Firebase
        existing_personnel[personnel_id] = personnel.to_dict()
        success = set_data("delivery_personnel", existing_personnel)

        if not success:
            management_bot.reply_to(
                message,
                f"❌ **Error Adding Personnel**\n\n"
                f"Failed to save personnel to database. Please try again.",
                parse_mode='Markdown'
            )
            return

        # Initialize earnings tracking
        from src.utils.earnings_utils import get_or_create_personnel_earnings
        earnings = get_or_create_personnel_earnings(personnel_id)

        # Save earnings to Firebase
        earnings_data = get_data("delivery_personnel_earnings") or {}
        earnings_data[personnel_id] = earnings.to_dict()
        set_data("delivery_personnel_earnings", earnings_data)

        # Initialize other data structures
        availability_data = get_data("delivery_personnel_availability") or {}
        capacity_data = get_data("delivery_personnel_capacity") or {}
        zones_data = get_data("delivery_personnel_zones") or {}
        performance_data = get_data("delivery_personnel_performance") or {}

        availability_data[personnel_id] = "offline"
        capacity_data[personnel_id] = 0
        zones_data[personnel_id] = ['1']
        performance_data[personnel_id] = {
            "total_deliveries": 0,
            "successful_deliveries": 0,
            "average_rating": 5.0,
            "total_distance": 0.0,
            "average_delivery_time": 0.0,
            "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # Save all data structures
        set_data("delivery_personnel_availability", availability_data)
        set_data("delivery_personnel_capacity", capacity_data)
        set_data("delivery_personnel_zones", zones_data)
        set_data("delivery_personnel_performance", performance_data)

        # Success message
        success_text = f"""
✅ **Personnel Added Successfully!**

**Details:**
• **Firestore ID:** `{personnel_id}`
• **Name:** {personnel_data['name']}
• **Phone:** {phone}
• **Telegram ID:** {telegram_id}
• **Service Areas:** 1 (default, can be edited)
• **Vehicle:** Motorcycle (default, can be edited)
• **Max Capacity:** 5 orders

**System Setup:**
• Status: Offline (until they log in)
• Verification: Pending (requires admin approval)
• Earnings Tracking: Initialized (0.00 birr)
• Performance Metrics: Initialized

**Next Steps:**
1. Personnel should start the delivery bot
2. Use 'Edit' function to configure service areas and vehicle
3. Admin can verify them in Personnel Management
        """

        # Add back button to return to personnel management
        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
        )

        management_bot.reply_to(
            message,
            success_text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        management_bot.reply_to(
            message,
            f"❌ **Error Adding Personnel**\n\n"
            f"An error occurred: {str(e)}\n\n"
            f"Please try again or contact system administrator.",
            parse_mode='Markdown'
        )

def start_remove_personnel(call):
    """Start the process of removing delivery personnel"""
    # Get all personnel
    personnel_data = get_data("delivery_personnel") or {}

    if not personnel_data:
        management_bot.edit_message_text(
            "❌ **No Personnel Found**\n\n"
            "There are no delivery personnel in the system to remove.",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )
        return

    # Create keyboard with personnel list
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for personnel_id, person in personnel_data.items():
        name = person.get('name', 'Unknown')
        status = person.get('status', 'unknown')
        button_text = f"🗑️ {name} ({status})"
        keyboard.add(
            types.InlineKeyboardButton(
                button_text,
                callback_data=f"remove_personnel_{personnel_id}"
            )
        )

    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
    )

    text = """
➖ **Remove Delivery Personnel**

Select a personnel member to remove from the system:

⚠️ **Warning:** This action cannot be undone!
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_personnel_list(call):
    """Show comprehensive list of all delivery personnel"""
    management_bot.answer_callback_query(call.id, "📋 Loading personnel list...")

    # Get all personnel-related data
    personnel_data = get_data("delivery_personnel") or {}
    availability_data = get_data("delivery_personnel_availability") or {}
    capacity_data = get_data("delivery_personnel_capacity") or {}

    if not personnel_data:
        text = """
📋 **Personnel List**

❌ **No Personnel Found**

There are no delivery personnel registered in the system.

**Available Actions:**
• Add new personnel
• Import personnel data
• Check system configuration
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("➕ Add Personnel", callback_data="pers_add")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Management", callback_data="mgmt_personnel")
        )
    else:
        # Calculate statistics
        total_personnel = len(personnel_data)
        verified_count = sum(1 for p in personnel_data.values() if p.get('is_verified', False))
        status_counts = {"available": 0, "busy": 0, "offline": 0, "inactive": 0}

        for person in personnel_data.values():
            status = person.get('status', 'offline')
            status_counts[status] = status_counts.get(status, 0) + 1

        text = f"""
📋 **Personnel List**

**📊 Summary:**
• Total: {total_personnel}
• Verified: {verified_count}/{total_personnel}
• Available: {status_counts.get('available', 0)}
• Busy: {status_counts.get('busy', 0)}
• Offline: {status_counts.get('offline', 0)}

**👥 Personnel Details:**
        """

        # Sort personnel by status and name
        sorted_personnel = sorted(
            personnel_data.items(),
            key=lambda x: (
                x[1].get('status', 'offline'),
                not x[1].get('is_verified', False),
                x[1].get('name', 'Unknown')
            )
        )

        for personnel_id, person in sorted_personnel:
            name = person.get('name', 'Unknown')
            phone = person.get('phone_number', 'N/A')
            status = person.get('status', 'offline')
            is_verified = person.get('is_verified', False)
            areas = person.get('service_areas', [])
            vehicle = person.get('vehicle_type', 'N/A')
            current_orders = capacity_data.get(personnel_id, 0)
            max_capacity = person.get('max_capacity', 5)

            # Status and verification emojis
            status_emoji = {
                'available': '✅',
                'busy': '🔄',
                'offline': '⭕',
                'inactive': '❌'
            }.get(status, '❓')

            verified_emoji = '✅' if is_verified else '⚠️'

            text += f"""
**{status_emoji}{verified_emoji} {name}**
• ID: `{personnel_id[:12]}...`
• Phone: {phone}
• Status: {status.title()}
• Orders: {current_orders}/{max_capacity}
• Vehicle: {vehicle.title()}
• Areas: {', '.join(areas[:3]) if areas else 'None'}{'...' if len(areas) > 3 else ''}
---
            """

        # Add action buttons
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            types.InlineKeyboardButton("➕ Add New", callback_data="pers_add"),
            types.InlineKeyboardButton("🔄 Refresh", callback_data="pers_list")
        )
        keyboard.add(
            types.InlineKeyboardButton("📊 Performance Report", callback_data="pers_performance"),
            types.InlineKeyboardButton("🔧 Bulk Actions", callback_data="pers_bulk")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Management", callback_data="mgmt_personnel")
        )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def start_personnel_search(call):
    """Start personnel search functionality"""
    management_bot.answer_callback_query(call.id, "🔍 Search feature coming soon!")

def show_personnel_performance(call):
    """Show comprehensive personnel performance analytics"""
    management_bot.answer_callback_query(call.id, "📊 Loading performance analytics...")

    # Get all performance-related data
    personnel_data = get_data("delivery_personnel") or {}
    performance_data = get_data("delivery_personnel_performance") or {}
    capacity_data = get_data("delivery_personnel_capacity") or {}

    if not personnel_data:
        text = """
📊 **Performance Analytics**

❌ **No Personnel Data**

There are no delivery personnel to analyze.
        """
        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
        )
    else:
        # Calculate overall statistics
        total_personnel = len(personnel_data)
        total_deliveries = 0
        total_successful = 0
        total_ratings = []
        active_personnel = 0
        verified_personnel = 0

        performance_list = []

        for personnel_id, person in personnel_data.items():
            name = person.get('name', 'Unknown')
            status = person.get('status', 'offline')
            is_verified = person.get('is_verified', False)

            # Get performance data
            perf = performance_data.get(personnel_id, {})
            deliveries = perf.get('total_deliveries', person.get('total_deliveries', 0))
            successful = perf.get('successful_deliveries', person.get('successful_deliveries', 0))
            rating = perf.get('average_rating', person.get('rating', 5.0))
            current_orders = capacity_data.get(personnel_id, 0)

            # Calculate success rate
            success_rate = (successful / deliveries * 100) if deliveries > 0 else 0

            # Add to totals
            total_deliveries += deliveries
            total_successful += successful
            if rating > 0:
                total_ratings.append(rating)
            if status in ['available', 'busy']:
                active_personnel += 1
            if is_verified:
                verified_personnel += 1

            performance_list.append({
                'name': name,
                'deliveries': deliveries,
                'successful': successful,
                'success_rate': success_rate,
                'rating': rating,
                'current_orders': current_orders,
                'status': status,
                'verified': is_verified
            })

        # Calculate overall metrics
        overall_success_rate = (total_successful / total_deliveries * 100) if total_deliveries > 0 else 0
        average_rating = sum(total_ratings) / len(total_ratings) if total_ratings else 0

        # Sort by performance (deliveries and success rate)
        performance_list.sort(key=lambda x: (x['deliveries'], x['success_rate']), reverse=True)

        text = f"""
📊 **Performance Analytics**

**🎯 Overall Metrics:**
• Total Personnel: {total_personnel}
• Active: {active_personnel} | Verified: {verified_personnel}
• Total Deliveries: {total_deliveries}
• Success Rate: {overall_success_rate:.1f}%
• Average Rating: {average_rating:.1f}/5.0

**🏆 Top Performers:**
        """

        # Show top 5 performers
        for i, performer in enumerate(performance_list[:5], 1):
            status_emoji = {
                'available': '✅',
                'busy': '🔄',
                'offline': '⭕',
                'inactive': '❌'
            }.get(performer['status'], '❓')

            verified_emoji = '✅' if performer['verified'] else '⚠️'

            text += f"""
**{i}. {status_emoji}{verified_emoji} {performer['name']}**
• Deliveries: {performer['deliveries']} | Success: {performer['success_rate']:.1f}%
• Rating: {performer['rating']:.1f}/5.0 | Current: {performer['current_orders']} orders
            """

        if len(performance_list) > 5:
            text += f"\n... and {len(performance_list) - 5} more personnel"

        keyboard = types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            types.InlineKeyboardButton("📈 Detailed Report", callback_data="perf_detailed"),
            types.InlineKeyboardButton("📊 Export Data", callback_data="perf_export")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="pers_performance"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_personnel")
        )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_personnel_status_management(call):
    """Show personnel status management interface"""
    management_bot.answer_callback_query(call.id, "🔧 Loading status management...")

    # Get personnel data
    personnel_data = get_data("delivery_personnel") or {}

    if not personnel_data:
        text = """
🔧 **Status Management**

❌ **No Personnel Found**

There are no delivery personnel to manage.
        """
        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
        )
    else:
        # Group by status
        status_groups = {"available": [], "busy": [], "offline": [], "inactive": []}

        for personnel_id, person in personnel_data.items():
            name = person.get('name', 'Unknown')
            status = person.get('status', 'offline')
            is_verified = person.get('is_verified', False)

            status_groups[status].append({
                'id': personnel_id,
                'name': name,
                'verified': is_verified
            })

        text = f"""
🔧 **Personnel Status Management**

**📊 Current Status Distribution:**
• Available: {len(status_groups['available'])}
• Busy: {len(status_groups['busy'])}
• Offline: {len(status_groups['offline'])}
• Inactive: {len(status_groups['inactive'])}

**🔧 Bulk Actions:**
        """

        keyboard = types.InlineKeyboardMarkup(row_width=2)
        keyboard.add(
            types.InlineKeyboardButton("✅ Activate All Offline", callback_data="bulk_activate"),
            types.InlineKeyboardButton("⭕ Set All Offline", callback_data="bulk_offline")
        )
        keyboard.add(
            types.InlineKeyboardButton("✅ Verify All Pending", callback_data="bulk_verify"),
            types.InlineKeyboardButton("⚠️ Unverify All", callback_data="bulk_unverify")
        )
        keyboard.add(
            types.InlineKeyboardButton("📋 Individual Status", callback_data="status_individual"),
            types.InlineKeyboardButton("🔄 Refresh", callback_data="pers_status")
        )
        keyboard.add(
            types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
        )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def handle_personnel_removal(call):
    """Handle personnel removal confirmation"""
    personnel_id = call.data.replace("remove_personnel_", "")

    # Get personnel details
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    name = person.get('name', 'Unknown')
    phone = person.get('phone_number', 'N/A')
    status = person.get('status', 'unknown')

    # Create confirmation keyboard
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("✅ Confirm Remove", callback_data=f"confirm_remove_{personnel_id}"),
        types.InlineKeyboardButton("❌ Cancel", callback_data="pers_remove")
    )

    text = f"""
⚠️ **Confirm Personnel Removal**

**Personnel Details:**
• **Name:** {name}
• **Phone:** {phone}
• **Status:** {status.title()}
• **ID:** `{personnel_id}`

**Warning:** This action will:
• Remove the personnel from the system
• Clear all their assignments
• Delete their performance history
• This action cannot be undone!

Are you sure you want to proceed?
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def confirm_personnel_removal(call):
    """Confirm and execute personnel removal"""
    personnel_id = call.data.replace("confirm_remove_", "")

    try:
        # Get personnel details before removal
        personnel_data = get_data("delivery_personnel") or {}
        person = personnel_data.get(personnel_id)

        if not person:
            management_bot.answer_callback_query(
                call.id,
                "❌ Personnel not found!",
                show_alert=True
            )
            return

        name = person.get('name', 'Unknown')

        # Remove personnel using utility function
        success = remove_delivery_personnel(personnel_id)

        if success:
            text = f"""
✅ **Personnel Removed Successfully**

**{name}** has been removed from the system.

All associated data including:
• Personnel record
• Assignment history
• Performance metrics
• Availability status

Has been permanently deleted.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
            )

            management_bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )

        else:
            management_bot.answer_callback_query(
                call.id,
                "❌ Failed to remove personnel. Please try again.",
                show_alert=True
            )

    except Exception as e:
        management_bot.answer_callback_query(
            call.id,
            f"❌ Error: {str(e)}",
            show_alert=True
        )

# Analytics Functions
def show_daily_analytics(call):
    """Show daily analytics summary"""
    try:
        today = datetime.datetime.now().strftime("%Y-%m-%d")

        # Get order data for today
        orders_data = get_data("orders") or {}
        today_orders = [
            order for order in orders_data.values()
            if order.get('timestamp', '').startswith(today)
        ]

        # Calculate metrics
        total_orders = len(today_orders)
        completed_orders = len([o for o in today_orders if o.get('status') == 'completed'])
        pending_orders = len([o for o in today_orders if o.get('status') in ['pending', 'processing']])

        # Calculate revenue (simplified - would need proper order totals)
        total_revenue = sum(
            float(order.get('total_amount', 0)) for order in today_orders
            if order.get('status') == 'completed'
        )

        # Get delivery personnel activity
        personnel_data = get_data("delivery_personnel") or {}
        active_personnel = len([p for p in personnel_data.values() if p.get('status') == 'available'])
        busy_personnel = len([p for p in personnel_data.values() if p.get('status') == 'busy'])

        text = f"""
📅 **Daily Analytics - {today}**

**Order Summary:**
• Total Orders: {total_orders}
• Completed: {completed_orders}
• Pending: {pending_orders}
• Success Rate: {(completed_orders/total_orders*100) if total_orders > 0 else 0:.1f}%

**Revenue:**
• Total Revenue: {total_revenue:.2f} birr
• Average Order: {(total_revenue/completed_orders) if completed_orders > 0 else 0:.2f} birr

**Personnel Status:**
• Active: {active_personnel}
• Busy: {busy_personnel}
• Total: {len(personnel_data)}

**Performance:**
• Orders per Personnel: {(completed_orders/len(personnel_data)) if len(personnel_data) > 0 else 0:.1f}
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_daily"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

    except Exception as e:
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_weekly_analytics(call):
    """Show weekly analytics summary"""
    try:
        # Calculate week range
        today = datetime.datetime.now()
        week_start = today - datetime.timedelta(days=today.weekday())
        week_end = week_start + datetime.timedelta(days=6)

        week_range = f"{week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}"

        # Get order data for this week
        orders_data = get_data("orders") or {}
        week_orders = []

        for order in orders_data.values():
            order_date = order.get('timestamp', '')
            if order_date:
                try:
                    order_datetime = datetime.datetime.fromisoformat(order_date.replace('Z', '+00:00'))
                    if week_start <= order_datetime <= week_end:
                        week_orders.append(order)
                except:
                    continue

        # Calculate metrics
        total_orders = len(week_orders)
        completed_orders = len([o for o in week_orders if o.get('status') == 'completed'])
        total_revenue = sum(
            float(order.get('total_amount', 0)) for order in week_orders
            if order.get('status') == 'completed'
        )

        # Daily breakdown
        daily_counts = {}
        for i in range(7):
            day = week_start + datetime.timedelta(days=i)
            day_str = day.strftime('%Y-%m-%d')
            daily_counts[day.strftime('%a')] = len([
                o for o in week_orders
                if o.get('timestamp', '').startswith(day_str)
            ])

        daily_breakdown = '\n'.join([f"• {day}: {count}" for day, count in daily_counts.items()])

        text = f"""
📊 **Weekly Analytics**
**Period:** {week_range}

**Summary:**
• Total Orders: {total_orders}
• Completed: {completed_orders}
• Total Revenue: {total_revenue:.2f} birr
• Daily Average: {(total_orders/7):.1f} orders

**Daily Breakdown:**
{daily_breakdown}

**Performance:**
• Completion Rate: {(completed_orders/total_orders*100) if total_orders > 0 else 0:.1f}%
• Average Revenue/Day: {(total_revenue/7):.2f} birr
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_weekly"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

    except Exception as e:
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_monthly_analytics(call):
    """Show monthly analytics summary"""
    management_bot.answer_callback_query(call.id, "📈 Monthly analytics coming soon!")

def show_transaction_analytics(call):
    """Show transaction count analytics"""
    management_bot.answer_callback_query(call.id, "🔢 Transaction analytics coming soon!")

def show_delivery_analytics(call):
    """Show delivery performance analytics"""
    try:
        # Get delivery personnel performance data
        personnel_data = get_data("delivery_personnel") or {}
        performance_data = get_data("delivery_personnel_performance") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}

        if not personnel_data:
            text = "❌ **No Delivery Data**\n\nNo delivery personnel found in the system."
        else:
            text = "🚚 **Delivery Analytics**\n\n"

            # Overall stats
            total_personnel = len(personnel_data)
            active_personnel = len([p for p in personnel_data.values() if p.get('status') == 'available'])
            busy_personnel = len([p for p in personnel_data.values() if p.get('status') == 'busy'])

            text += f"""**Personnel Overview:**
• Total Personnel: {total_personnel}
• Currently Active: {active_personnel}
• Currently Busy: {busy_personnel}
• Offline: {total_personnel - active_personnel - busy_personnel}

**Top Performers:**
            """

            # Get top performers (simplified)
            performer_stats = []
            for personnel_id, person in personnel_data.items():
                name = person.get('name', 'Unknown')
                perf = performance_data.get(personnel_id, {})
                completed = perf.get('completed_deliveries', 0)
                performer_stats.append((name, completed))

            # Sort by completed deliveries
            performer_stats.sort(key=lambda x: x[1], reverse=True)

            for i, (name, completed) in enumerate(performer_stats[:5], 1):
                text += f"\n{i}. {name}: {completed} deliveries"

            # Current assignments
            active_assignments = len([a for a in assignments_data.values() if a.get('status') == 'active'])
            text += f"\n\n**Current Status:**\n• Active Assignments: {active_assignments}"

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_delivery"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

    except Exception as e:
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_trend_analytics(call):
    """Show trend analysis"""
    management_bot.answer_callback_query(call.id, "💹 Trend analysis coming soon!")

def handle_personnel_update(call):
    """Stub for personnel update callback - not yet implemented"""
    management_bot.answer_callback_query(call.id, "⚠️ Personnel update not implemented yet.")

def register_management_bot_handlers():
    """Register all management bot handlers"""
    try:
        # Register start and help command handler
        management_bot.register_message_handler(handle_start, commands=['start', 'help'])

        # Register callback query handler
        management_bot.register_callback_query_handler(handle_callback_query, func=lambda call: True)

        # Register text message handlers for personnel addition (with lower priority)
        management_bot.register_message_handler(process_add_personnel, func=lambda message: message.content_type == 'text' and not message.text.startswith('/'))

    except Exception as e:
        raise

def run_management_bot():
    """Run the management bot"""
    try:
        # Register handlers first
        register_management_bot_handlers()

        # Start polling
        management_bot.polling(none_stop=True, interval=1)
    except Exception as e:
        raise

# Edit Personnel Functions
def start_edit_name(call):
    """Start editing personnel name"""
    personnel_id = call.data.replace("edit_name_", "")
    management_bot.answer_callback_query(call.id, "📝 Edit name...")

    # Get current name
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(call.id, "❌ Personnel not found!", show_alert=True)
        return

    current_name = person.get('name', 'Unknown')

    text = f"""
📝 **Edit Name**

**Current Name:** {current_name}

Please reply with the new name for this personnel:
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Store edit context and set up handler
    management_bot.register_next_step_handler(
        call.message,
        lambda msg: process_name_edit(msg, personnel_id)
    )

def start_edit_phone(call):
    """Start editing personnel phone"""
    personnel_id = call.data.replace("edit_phone_", "")
    management_bot.answer_callback_query(call.id, "📞 Edit phone...")

    # Get current phone
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(call.id, "❌ Personnel not found!", show_alert=True)
        return

    current_phone = person.get('phone_number', 'N/A')

    text = f"""
📞 **Edit Phone Number**

**Current Phone:** {current_phone}

Please reply with the new phone number (include country code):
**Example:** +251912345678
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Store edit context and set up handler
    management_bot.register_next_step_handler(
        call.message,
        lambda msg: process_phone_edit(msg, personnel_id)
    )

def start_edit_telegram_id(call):
    """Start editing personnel Telegram ID"""
    personnel_id = call.data.replace("edit_telegram_", "")
    management_bot.answer_callback_query(call.id, "📱 Edit Telegram ID...")

    # Get current Telegram ID
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(call.id, "❌ Personnel not found!", show_alert=True)
        return

    current_telegram_id = person.get('telegram_id', 'N/A')

    text = f"""
📱 **Edit Telegram ID**

**Current Telegram ID:** {current_telegram_id}

Please reply with the new Telegram ID (numbers only):
**Example:** 123456789
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Store edit context and set up handler
    management_bot.register_next_step_handler(
        call.message,
        lambda msg: process_telegram_id_edit(msg, personnel_id)
    )

def process_name_edit(message, personnel_id):
    """Process name edit"""
    try:
        new_name = message.text.strip()

        if not new_name or len(new_name) < 2:
            management_bot.reply_to(
                message,
                "❌ **Invalid Name**\n\nName must be at least 2 characters long.",
                parse_mode='Markdown'
            )
            return

        # Update personnel data
        personnel_data = get_data("delivery_personnel") or {}
        if personnel_id not in personnel_data:
            management_bot.reply_to(
                message,
                "❌ **Personnel Not Found**\n\nPersonnel record not found in database.",
                parse_mode='Markdown'
            )
            return

        old_name = personnel_data[personnel_id].get('name', 'Unknown')
        personnel_data[personnel_id]['name'] = new_name

        # Save to Firebase
        success = set_data("delivery_personnel", personnel_data)

        if success:
            # Create success message with back button
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )

            management_bot.reply_to(
                message,
                f"✅ **Name Updated Successfully!**\n\n"
                f"**Old Name:** {old_name}\n"
                f"**New Name:** {new_name}\n\n"
                f"All earnings data and performance metrics have been preserved.",
                reply_markup=keyboard,
                parse_mode='Markdown'
            )
        else:
            management_bot.reply_to(
                message,
                "❌ **Update Failed**\n\nFailed to save changes to database. Please try again.",
                parse_mode='Markdown'
            )

    except Exception as e:
        management_bot.reply_to(
            message,
            f"❌ **Error**\n\nAn error occurred: {str(e)}",
            parse_mode='Markdown'
        )

def process_phone_edit(message, personnel_id):
    """Process phone edit"""
    try:
        new_phone = message.text.strip()

        # Basic phone validation
        if not new_phone or len(new_phone) < 10:
            management_bot.reply_to(
                message,
                "❌ **Invalid Phone Number**\n\nPhone number must be at least 10 characters long.",
                parse_mode='Markdown'
            )
            return

        if not (new_phone.startswith('+') or new_phone.startswith('0')):
            management_bot.reply_to(
                message,
                "❌ **Invalid Phone Format**\n\nPhone number should start with '+' (international) or '0' (local).",
                parse_mode='Markdown'
            )
            return

        # Update personnel data
        personnel_data = get_data("delivery_personnel") or {}
        if personnel_id not in personnel_data:
            management_bot.reply_to(
                message,
                "❌ **Personnel Not Found**\n\nPersonnel record not found in database.",
                parse_mode='Markdown'
            )
            return

        old_phone = personnel_data[personnel_id].get('phone_number', 'N/A')
        personnel_data[personnel_id]['phone_number'] = new_phone

        # Save to Firebase
        success = set_data("delivery_personnel", personnel_data)

        if success:
            # Create success message with back button
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )

            management_bot.reply_to(
                message,
                f"✅ **Phone Updated Successfully!**\n\n"
                f"**Old Phone:** {old_phone}\n"
                f"**New Phone:** {new_phone}\n\n"
                f"All earnings data and performance metrics have been preserved.",
                reply_markup=keyboard,
                parse_mode='Markdown'
            )
        else:
            management_bot.reply_to(
                message,
                "❌ **Update Failed**\n\nFailed to save changes to database. Please try again.",
                parse_mode='Markdown'
            )

    except Exception as e:
        management_bot.reply_to(
            message,
            f"❌ **Error**\n\nAn error occurred: {str(e)}",
            parse_mode='Markdown'
        )

def process_telegram_id_edit(message, personnel_id):
    """Process Telegram ID edit"""
    try:
        new_telegram_id = message.text.strip()

        # Validate Telegram ID format
        if not new_telegram_id.isdigit():
            management_bot.reply_to(
                message,
                f"❌ **Invalid Telegram ID**\n\nTelegram ID must be numeric. You provided: {new_telegram_id}",
                parse_mode='Markdown'
            )
            return

        # Check if Telegram ID already exists
        personnel_data = get_data("delivery_personnel") or {}
        for pid, pdata in personnel_data.items():
            if pid != personnel_id and pdata.get('telegram_id') == new_telegram_id:
                management_bot.reply_to(
                    message,
                    f"❌ **Telegram ID Already Exists**\n\n"
                    f"A personnel with Telegram ID {new_telegram_id} already exists.\n\n"
                    f"Personnel: {pdata.get('name', 'Unknown')} (ID: {pid})",
                    parse_mode='Markdown'
                )
                return

        if personnel_id not in personnel_data:
            management_bot.reply_to(
                message,
                "❌ **Personnel Not Found**\n\nPersonnel record not found in database.",
                parse_mode='Markdown'
            )
            return

        old_telegram_id = personnel_data[personnel_id].get('telegram_id', 'N/A')
        personnel_data[personnel_id]['telegram_id'] = new_telegram_id

        # Save to Firebase
        success = set_data("delivery_personnel", personnel_data)

        if success:
            # Create success message with back button
            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel Management", callback_data="mgmt_personnel")
            )

            management_bot.reply_to(
                message,
                f"✅ **Telegram ID Updated Successfully!**\n\n"
                f"**Old Telegram ID:** {old_telegram_id}\n"
                f"**New Telegram ID:** {new_telegram_id}\n\n"
                f"All earnings data and performance metrics have been preserved.",
                reply_markup=keyboard,
                parse_mode='Markdown'
            )
        else:
            management_bot.reply_to(
                message,
                "❌ **Update Failed**\n\nFailed to save changes to database. Please try again.",
                parse_mode='Markdown'
            )

    except Exception as e:
        management_bot.reply_to(
            message,
            f"❌ **Error**\n\nAn error occurred: {str(e)}",
            parse_mode='Markdown'
        )

def backup_delivery_personnel():
    # Export all delivery personnel data from Firestore to a local JSON file
    try:
        from src.firebase_db import get_data
        personnel = get_data('delivery_personnel')
        with open('delivery_personnel_backup.json', 'w', encoding='utf-8') as f:
            json.dump(personnel, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"Failed to backup delivery personnel: {e}")
        notify_admin_error(f"Failed to backup delivery personnel: {e}")
        return False

@management_bot.message_handler(commands=['backup_personnel'])
def handle_backup_personnel(message):
    user_id = message.from_user.id
    if not is_admin(user_id):
        management_bot.reply_to(message, "❌ You are not authorized to use this bot.")
        return
    if is_rate_limited(user_id):
        management_bot.reply_to(message, "⏳ Too many actions. Please wait a minute and try again.")
        return
    if backup_delivery_personnel():
        management_bot.reply_to(message, "✅ Delivery personnel data backed up to delivery_personnel_backup.json")
    else:
        management_bot.reply_to(message, "❗️ Failed to backup delivery personnel data. The admin has been notified.")

if __name__ == "__main__":
    run_management_bot()
